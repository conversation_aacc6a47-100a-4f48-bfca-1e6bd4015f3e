autonomy_state = {
	id = autonomy_state_government
	
	is_puppet = yes
	
	min_freedom_level = 0.0
	
	manpower_influence = 0
	use_overlord_color = yes
	rule = {
		desc = "RULE_DESC_IS_A_SUBJECT"
		
		can_not_declare_war = yes
		can_decline_call_to_war = no
		units_deployed_to_overlord = yes
		can_be_spymaster = no
		contributes_operatives = yes
		can_create_collaboration_government = no
	}
	
	modifier = {
		autonomy_manpower_share = 1
		can_master_build_for_us = 1
		extra_trade_to_overlord_factor = 1.0
		overlord_trade_cost_factor = -0.8
		cic_to_overlord_factor = 0.35
		mic_to_overlord_factor = 0.65
		autonomy_gain_global_factor = -0.3
		peace_score_ratio_transferred_to_overlord = 0.5
		lend_lease_tension_with_overlord = -0.40
	}
	
	ai_subject_wants_higher = {
		factor = 0
	}
	
	ai_overlord_wants_lower = {
		factor = 0.0
	}

	ai_overlord_wants_garrison = {
		always = yes
	}

	allowed = {
		always = yes
	}
	
	can_take_level = {
		always = no
	}

	can_lose_level = {
		always = no
	}
}
