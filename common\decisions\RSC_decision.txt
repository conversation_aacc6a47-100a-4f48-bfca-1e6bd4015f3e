RSC_selling_tech_decision = {
	sell_with_YBT = {
		visible = {
			country_exists = YBT
			YBT = {
				has_war = no
				NOT = {
					has_idea = imp_hz
				}
			}
			RSC = {
				has_war = no
			}
			tag = RSC
			has_completed_focus = RSC_selling_tech
		}
		days_re_enable = 30
		cost = 50
		complete_effect = {
			YBT = {
				country_event = {id = RSC_b.1 days = 3}
			}
		}
	}
	sell_with_PSA = {
		visible = {
			country_exists = PSA
			PSA = {
				has_war = no
				NOT = {
					has_idea = imp_hz
				}
			}
			RSC = {
				has_war = no
			}
			tag = RSC
			has_completed_focus = RSC_selling_tech
		}
		days_re_enable = 30
		cost = 50
		complete_effect = {
			PSA = {
				country_event = {id = RSC_b.1 days = 3}
			}
		}
	}
	sell_with_CPR = {
		visible = {
			country_exists = CPR
			CPR = {
				has_war = no
				NOT = {
					has_idea = imp_hz
				}
			}
			RSC = {
				has_war = no
			}
			tag = RSC
			has_completed_focus = RSC_selling_tech
		}
		days_re_enable = 30
		cost = 50
		complete_effect = {
			CPR = {
				country_event = {id = RSC_b.1 days = 3}
			}
		}
	}
	sell_with_KMT = {
		visible = {
			country_exists = KMT
			KMT = {
				has_war = no
				NOT = {
					has_idea = imp_hz
				}
			}
			RSC = {
				has_war = no
			}
			tag = RSC
			has_completed_focus = RSC_selling_tech
		}
		days_re_enable = 30
		cost = 50
		complete_effect = {
			KMT = {
				country_event = {id = RSC_b.1 days = 3}
			}
		}
	}
	sell_with_RDE = {
		visible = {
			country_exists = RDE
			RDE = {
				has_war = no
				NOT = {
					has_idea = imp_hz
				}
			}
			RSC = {
				has_war = no
			}
			tag = RSC
			has_completed_focus = RSC_selling_tech
		}
		days_re_enable = 30
		cost = 50
		complete_effect = {
			RDE ={
				country_event = {id = RSC_b.1 days = 3}
			}
		}
	}
	sell_with_MSA = {
		visible = {
			country_exists = MSA
			MSA = {
				has_war = no
				NOT = {
					has_idea = imp_hz
				}
			}
			RSC = {
				has_war = no
			}
			tag = RSC
			has_completed_focus = RSC_selling_tech
		}
		days_re_enable = 30
		cost = 50
		complete_effect = {
			MSA = {
				country_event = {id = RSC_b.1 days = 3}
			}
		}
	}
	sell_with_KAM = {
		visible = {
			country_exists = KAM
			KAM = {
				has_war = no
				NOT = {
					has_idea = imp_hz
				}
			}
			RSC = {
				has_war = no
			}
			tag = RSC
			has_completed_focus = RSC_selling_tech
		}
		days_re_enable = 30
		cost = 50
		complete_effect = {
			KAM = {
				country_event = {id = RSC_b.1 days = 3}
			}
		}
	}
}
RSCP_Academy_Sciences_decision = {
	imp_with_YBT = {
		visible = {
			country_exists = YBT
			YBT = {
				has_war = no
				has_idea = imp_hz
			}
			RSC = {
				has_war = no
			}
			tag = RSC
			has_completed_focus = RSCP_Academy_Sciences
		}
		days_re_enable = 30
		cost = 50
		complete_effect = {
			YBT = {
				country_event = {id = RSC_b.3 days = 3}
			}
		}
	}
	imp_with_PSA = {
		visible = {
			country_exists = PSA
			PSA = {
				has_war = no
				has_idea = imp_hz
			}
			RSC = {
				has_war = no
			}
			tag = RSC
			has_completed_focus = RSCP_Academy_Sciences
		}
		days_re_enable = 30
		cost = 50
		complete_effect = {
			PSA = {
				country_event = {id = RSC_b.3 days = 3}
			}
		}
	}
	imp_with_CPR = {
		visible = {
			country_exists = CPR
			CPR = {
				has_war = no
				has_idea = imp_hz
			}
			RSC = {
				has_war = no
			}
			ROOT = {
				tag = RSC
			}
		}
		days_re_enable = 30
		cost = 50
		complete_effect = {
			CPR = {
				country_event = {id = RSC_b.3 days = 3}
			}
		}
	}
	imp_with_KMT = {
		visible = {
			country_exists = KMT
			KMT = {
				has_war = no
				has_idea = imp_hz
			}
			RSC = {
				has_war = no
			}
			tag = RSC
			has_completed_focus = RSCP_Academy_Sciences
		}
		days_re_enable = 30
		cost = 50
		complete_effect = {
			KMT = {
				country_event = {id = RSC_b.3 days = 3}
			}
		}
	}
	imp_with_RDE = {
		visible = {
			country_exists = RDE
			RDE = {
				has_war = no
				has_idea = imp_hz
			}
			RSC = {
				has_war = no
			}
			tag = RSC
			has_completed_focus = RSCP_Academy_Sciences
		}
		days_re_enable = 30
		cost = 50
		complete_effect = {
			RDE ={
				country_event = {id = RSC_b.3 days = 3}
			}
		}
	}
	imp_with_MSA = {
		visible = {
			country_exists = MSA
			MSA = {
				has_war = no
				has_idea = imp_hz
			}
			RSC = {
				has_war = no
			}
			tag = RSC
			has_completed_focus = RSCP_Academy_Sciences
		}
		days_re_enable = 30
		cost = 50
		complete_effect = {
			MSA = {
				country_event = {id = RSC_b.3 days = 3}
			}
		}
	}
	imp_with_KAM = {
		visible = {
			country_exists = KAM
			KAM = {
				has_war = no
				has_idea = imp_hz
			}
			RSC = {
				has_war = no
			}
			tag = RSC
			has_completed_focus = RSCP_Academy_Sciences
		}
		days_re_enable = 30
		cost = 50
		complete_effect = {
			KAM = {
				country_event = {id = RSC_b.3 days = 3}
			}
		}
	}
}
RSC_imp_hz_c = {
	RSC_imp_hz = {
		visible = {
			country_exists = RSC
			NOT = {
				tag = RSC
			}
			has_idea = get_tech
		}
		fire_only_once = yes
		ai_will_do = {
			factor = 1
		}
		complete_effect = {
			country_event = {id = RSC_b.2 days = 1}
		}
	}
}
Rus_political_struggle = {
	lose_controller_id = {
		activation = {
			has_country_flag = Rus_controller
		}
		days_mission_timeout = 7000
		available = {
			if = {
				limit = {
					has_war = yes
				}
				surrender_progress < 0.5
				NOT = {
					has_full_control_of_state = 219
				}
			}
			else = {
				NOT = {
					or = {
						is_subject = no
						has_full_control_of_state = 219
					}
				}
			}
		}
		is_good = no
		complete_effect = {
			ROOT = {
				clr_country_flag = Rus_controller
			}
			custom_effect_tooltip = rus_controller_waring_2
			custom_effect_tooltip = rus_controller_waring
		}
		timeout_effect = {
			unlock_decision_tooltip = lose_controller_id
		}
	}
	puppet_of_rus_state_government = {
		visible = {
			ROOT = {
				has_country_flag = Rus_controller
				not={tag=from}
			}
			FROM = {
				NOT = {
					has_war_with = ROOT
					has_country_flag = Rus_controller
				}
			}
			if = {
				limit = {
					tag =RSC
				}
				has_completed_focus = RSC_freedom_rus
			}
		}
		target_trigger = {
			from = {
				has_country_flag = Rus_uncontroller
			}
		}
		ai_will_do = {
			factor = 10
		}
		cost = 150
		fire_only_once = yes
		complete_effect = {
			from = {
				country_event = {id = Rus_puppet_state_government.1 days = 1}
			}
			custom_effect_tooltip = rus_state_government_tooltip1
			custom_effect_tooltip = rus_state_government_tooltip2
			add_to_variable = {
				RSC_state_government_request = 1
			}
		}
	}
	annex_of_rus_state_government_rus = {
		visible = {
			from = {
				is_subject_of = ROOT
				has_country_flag = rus_state_government
				#has_completed_focus = RSC_Persuasion
			}
			if = {
				limit = {
					tag = RSC
				}
				has_completed_focus = RSC_FarEast_Petrograd
			}
		}
		target_trigger = {
			from = {
				has_country_flag = Rus_uncontroller
			}
		}
		ai_will_do = {
			factor = 1
			modifier = {
				factor = 55555555
				if = {
					limit = {
						not = {
							has_war = yes
						}
						from = {
							is_subject_of = ROOT
						}
					}
				}
			}
		}
		cost = 150
		complete_effect = {
			every_state = {
				if = {
					limit = {
						is_owned_by = from
					}
					add_core_of = ROOT
				}
			}
			annex_country = {
				target = from
				transfer_troops = yes
			}
		}
	}
	give_annex_tooltip_rus = {
		visible = {
			ROOT = {
				has_country_flag = Rus_controller
			}
			from = {
				not = {
					is_subject_of = ROOT
				}
			}
			if = {
				limit = {
					tag = RSC
				}
				has_completed_focus = RSC_Persuasion
			}
		}
		target_trigger = {
			from = {
				has_country_flag = rus_not_state_government
			}
		}
		ai_will_do = {
			factor = 1
			modifier = {
				factor = 55555555
				if = {
					limit = {
						not = {
							has_war = yes
						}
						RSC = {
							is_subject_of = ROOT
						}
					}
				}
			}
		}
		cost = 150
		fire_only_once = yes
		complete_effect = {
			from = {
				country_event = {id=give_annex_tooltip_e.1 days=1}
			}
		}
	}
	fa_ya_de_hong_rsc = {
		visible = {
			has_completed_focus = RSC_Persuasion
			from = {
				has_war_with = RSC
			}
		}
		target_trigger = {
			from = {
				or = {
					original_tag = YDS
					original_tag = POA
				}
			}
		}
		ai_will_do = {
			factor = 1000
		}
		cost = 25
		fire_only_once = yes
		complete_effect = {
			from = {
				country_event = {id = RSC_UT.9 days = 1}
			}
		}
	}
}

RSC_KDR_bop_category = {

	# 科学侧决策
	RSC_KDR_increase_science_middle = {
		icon = ger_mefo_bills
		
		visible = {
			has_completed_focus = RSC_b2_1
		}
		modifier = {
			political_power_gain = -0.2
		}
		cancel_if_not_visible = yes
		fire_only_once = no
		cost = 50
		days_remove = 10
		remove_effect = {
			add_power_balance_value = {
				id = RSC_KDR_bop
				value = 0.1
				tooltip_side = RSC_build_side
			}
		}
	}

	RSC_KDR_increase_science_small = {
		icon = ger_mefo_bills
		
		visible = {
			has_completed_focus = RSC_b2
		}
		modifier = {
            political_power_gain = -0.1
        }
		cancel_if_not_visible = yes
		fire_only_once = no
		cost = 25
		days_remove = 5
		remove_effect = {
			add_power_balance_value = {
				id = RSC_KDR_bop
				value = 0.05
                tooltip_side = RSC_build_side
			}
		}
	}
	# 建设侧决策
	RSC_KDR_increase_build_middle = {
		icon = ger_mefo_bills
		
		visible = {
			has_completed_focus = RSC_b2_2
		}
		modifier = {
			political_power_gain = -0.2
		}
		cancel_if_not_visible = yes
		fire_only_once = no
		cost = 50
		days_remove = 10
		remove_effect = {
			add_power_balance_value = {
				id = RSC_KDR_bop
				value = -0.1
				tooltip_side = RSC_build_side
			}
		}
	}

	RSC_KDR_increase_build_small = {
		icon = ger_mefo_bills
		
		visible = {
			has_completed_focus = RSC_b2
		}
		
		cancel_if_not_visible = yes
		fire_only_once = no
		cost = 25
		modifier = {
            political_power_gain = -0.1
        }
		days_remove = 5
		remove_effect = {
			add_power_balance_value = {
				id = RSC_KDR_bop
				value = -0.05
                tooltip_side = RSC_build_side
			}
		}
	}

	RSC_KDR_increase_science_big = {
		icon = ger_mefo_bills
		
		visible = {
			has_completed_focus = RSC_b2_3
		}
		
		cancel_if_not_visible = yes
		fire_only_once = no
		cost = 100
		modifier = {
            political_power_gain = -0.3
        }
		days_remove = 15
		remove_effect = {
			add_power_balance_value = {
				id = RSC_KDR_bop
				value = 0.15
                tooltip_side = RSC_science_side
			}
		}
	}

	RSC_KDR_increase_build_big = {
		icon = ger_mefo_bills
		
		visible = {
			has_completed_focus = RSC_b2_4
		}
		
		cancel_if_not_visible = yes
		fire_only_once = no
		cost = 100
		modifier = {
            political_power_gain = -0.3
        }
		days_remove = 15
		remove_effect = {
			add_power_balance_value = {
				id = RSC_KDR_bop
				value = -0.15
                tooltip_side = RSC_build_side
			}
		}
	}
	RSC_KDR_increase_science_super = {
		icon = ger_mefo_bills
		visible = {
			has_completed_focus = RSC_b2_6
		}
		modifier = {
			political_power_gain = -0.4
		}
		cancel_if_not_visible = yes
		fire_only_once = no
		cost = 150
		days_remove = 20
		remove_effect = {
			add_power_balance_value = {
				id = RSC_KDR_bop
				value = 0.35
				tooltip_side = RSC_science_side
			}
		}
	}
	RSC_KDR_increase_build_super = {
		icon = ger_mefo_bills
		visible = {
			has_completed_focus = RSC_b2_5
		}
		modifier = {
			political_power_gain = -0.4
		}
		cancel_if_not_visible = yes
		fire_only_once = no
		cost = 150
		days_remove = 20
		remove_effect = {
			add_power_balance_value = {
				id = RSC_KDR_bop
				value = -0.35
				tooltip_side = RSC_build_side
			}
		}
	}
}

RYH_balance_category = {
	RYH_balance_view_decision = {
		icon = generic_information
		visible = {
			has_completed_focus = RYH_544
		}
		# always visible/available as category governs conditions
		cost = 0
		days_remove = 1
		complete_effect = {
		}
		remove_effect = {
		}
	}
	RYH_balance_getD = {
		icon = generic_information
		visible = {
			has_completed_focus = RYH_544
		}
		# always visible/available as category governs conditions
		cost = 25
		days_re_enable = 7
		days_remove = 3
		remove_effect = {
			add_to_variable = {
				RYH_544_flag_d = 2
			}
			subtract_from_variable = {
				RYH_544_flag_c2 = 1
			}
			custom_effect_tooltip = RYHDTooltipDA
			custom_effect_tooltip = RYHDTooltipC2S
		}
	}
	RYH_balance_getF = {
		icon = generic_information
		visible = {
			has_completed_focus = RYH_544
		}
		cost = 25
		days_re_enable = 7
		days_remove = 3
		remove_effect = {
			add_to_variable = {
				RYH_544_flag_f = 2
			}
			subtract_from_variable = {
				RYH_544_flag_e = 1
			}
			custom_effect_tooltip = RYHDTooltipFA
			custom_effect_tooltip = RYHDTooltipES
		}
	}
	RYH_balance_getE = {
		icon = generic_information
		visible = {
			has_completed_focus = RYH_544
		}
		cost = 25
		days_re_enable = 7
		days_remove = 3
		remove_effect = {
			add_to_variable = {
				RYH_544_flag_e = 2
			}
			subtract_from_variable = {
				RYH_544_flag_f = 1
			}
			custom_effect_tooltip = RYHDTooltipEA
			custom_effect_tooltip = RYHDTooltipFS
		}
	}
	RYH_balance_getN = {
		icon = generic_information
		visible = {
			has_completed_focus = RYH_544
		}
		cost = 25
		days_re_enable = 7
		days_remove = 3
		remove_effect = {
			add_to_variable = {
				RYH_544_flag_n = 1
			}
			custom_effect_tooltip = RYHDTooltipNA
		}
	}
	RYH_balance_getC1 = {
		icon = generic_information
		visible = {
			has_completed_focus = RYH_544
		}
		cost = 25
		days_re_enable = 7
		days_remove = 3
		remove_effect = {
			add_to_variable = {
				RYH_544_flag_c2 = 2
			}
			subtract_from_variable = {
				RYH_544_flag_c1 = 1
			}
			custom_effect_tooltip = RYHDTooltipC1A
			custom_effect_tooltip = RYHDTooltipC1S
		}
	}
}