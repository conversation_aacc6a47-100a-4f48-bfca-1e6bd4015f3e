focus_tree = {
	id = RSC
	country = {
		factor = 0
		modifier = {
			add = 10
			tag = RSC
		}
	}
	default = no
	focus = {
		id = RSC_ob_situation
		icon = GFX_goal_generic_neutrality_focus
		completion_reward = {
			add_stability = 0.1
			add_war_support = 0.05
			add_political_power = 100
			set_party_name = {
				ideology = neutrality
				long_name = "科学院"
				name = "科学院"
			}
		}
		cost = 5
		x = 21
		y = 0
	}
	focus = {
		id = RSC_SecondElection
		icon = GFX_goal_support_democracy
		cost = 5
		prerequisite = {
			focus = RSC_ob_situation
		}
		completion_reward = {
			hidden_effect = {
				RSC = {
					country_event = {
						id = RSC_a.1
						hours = 1
					}
				}
			}
		}
		x = 21
		y = 1
	}
	focus = {
		id = RSC_KardashevRoad
		icon = GFX_goal_generic_political_pressure
		cost = 0
		prerequisite = {
			focus = RSC_SecondElection
		}
		available = {
			always = no
		}
		completion_reward = {
			add_popularity = {
				ideology = communism
				popularity = 0.7
			}
			create_faction = RSC_fact1
			create_country_leader = {
				name = "尼古拉·谢苗诺维奇·卡尔达舍夫"
				picture = GFX_LEADER_RSC_nigorla
				ideology = stalinism
				traits = {
					RSC_L_GJYS
					RSC_L_WDLY
				}
			}
			set_politics = {
				ruling_party = communism
			}
			set_party_name = {
				ideology = neutrality
				long_name = "中立主义"
				name = "中立主义"
			}
			set_party_name = {
				ideology = communism
				long_name = "科学院"
				name = "科学院"
			}
		}
		x = 15
		y = 2
	}
	focus = {
		id = RSC_freedom_rus
		icon = GFX_goal_support_democracy
		cost = 5
		prerequisite = {
			focus = RSC_KardashevRoad
		}
		completion_reward = {
			set_country_flag = Rus_part_of_political_struggle
			set_country_flag = Rus_controller
			clr_country_flag = Rus_uncontroller
			custom_effect_tooltip = custom_rus_political_struggle
			if = {
				limit = {
					POA = {
						not = {
							has_country_flag = Rus_controller
						}
					}
				}
				POA = {
					set_country_flag = Rus_uncontroller
				}
			}
			if = {
				limit = {
					PRR = {
						not = {
							has_country_flag = Rus_controller
						}
					}
				}
				PRR = {
					set_country_flag = Rus_uncontroller
				}
			}
			if = {
				limit = {
					UNS = {
						not = {
							has_country_flag = Rus_controller
						}
					}
				}
				UNS = {
					set_country_flag = Rus_uncontroller
				}
			}
			if = {
				limit = {
					NRU = {
						not = {
							has_country_flag = Rus_controller
						}
					}
				}
				NRU = {
					set_country_flag = Rus_uncontroller
				}
			}
			if = {
				limit = {
					RUE = {
						not = {
							has_country_flag = Rus_controller
						}
					}
				}
				RUE = {
					set_country_flag = Rus_uncontroller
				}
			}
			if = {
				limit = {
					SNP = {
						not = {
							has_country_flag = Rus_controller
						}
					}
				}
				SNP = {
					set_country_flag = Rus_uncontroller
				}
			}
			if = {
				limit = {
					YDS = {
						not = {
							has_country_flag = Rus_controller
						}
					}
				}
				YDS = {
					set_country_flag = Rus_uncontroller
				}
			}
		}
		x = 8
		y = 3
	}
	focus = {
		id = RSC_stop_inhuman_actor
		icon = GFX_focus_GER_subdue_the_wehrmacht
		cost = 7
		prerequisite = {
			focus = RSC_KardashevRoad
		}
		completion_reward = {
			add_ideas = RSC_RDSYF
		}
		x = 10
		y = 3
	}
	focus = {
		id = RSCP_Academy_Sciences
		icon = GFX_focus_NORDIC_dem_capstone_research
		cost = 10
		prerequisite = {
			focus = RSC_stop_inhuman_actor
		}
		completion_reward = {
			add_research_slot = 1
			unlock_decision_category_tooltip = RSCP_Academy_Sciences_decision
		}
		x = 10
		y = 4
	}
	focus = {
		id = RSC_Impr_military_tech
		icon = GFX_goal_generic_scientific_exchange
		cost = 10
		prerequisite = {
			focus = RSC_KardashevRoad
		}
		completion_reward = {
			add_tech_bonus = {
				bonus = 0.5
				uses = 2
				category = infantry_weapons
			}
			add_tech_bonus = {
				bonus = 0.5
				uses = 1
				category = artillery
			}
		}
		x = 21
		y = 3
	}
	focus = {
		id = RSC_selling_tech
		icon = GFX_focus_NORDIC_dem_institution_1
		cost = 5
		prerequisite = {
			focus = RSC_stop_inhuman_actor
			focus = RSC_Impr_military_tech
		}
		prerequisite = {
			focus = RSC_b2
		}
		completion_reward = {
			unlock_decision_category_tooltip = RSC_selling_tech_decision
		}
		x = 12
		y = 4
	}
	focus = {
		id = RSC_everybodyRun
		icon = GFX_goal_generic_allies_build_infantry
		cost = 10
		prerequisite = {
			focus = RSCP_Academy_Sciences
		}
		prerequisite = {
			focus = RSC_selling_tech
		}
		bypass = {
		}
		completion_reward = {
			if = {
				limit = {
					or = {
						country_exists = POA
						country_exists = PRR
						country_exists = YDS
						country_exists = SNP
						country_exists = RUE
						country_exists = NRU
					}
				}
				add_timed_idea = {
					idea = RSC_DBG
					days = 365
				}
				add_ideas = tot_economic_mobilisation
			}
			else = {
				add_ideas = tot_economic_mobilisation
			}
			custom_effect_tooltip = rus_everybody_run_effect
		}
		x = 10
		y = 6
	}
	focus = {
		id = RSC_Organizational_Industrial
		icon = GFX_goal_generic_consumer_goods
		cost = 10
		prerequisite = {
			focus = RSC_small_space
		}
		completion_reward = {
			add_timed_idea = {
				idea = RSC_WNJH
				days = 365
			}
			hidden_effect = {
				every_state = {
					if = {
						limit = {
							is_capital = yes
							is_owned_by = RSC
						}
						add_extra_state_shared_building_slots = 3
						add_building_construction = {
							type = arms_factory
							level = 1
							instant_build = yes
						}
						add_building_construction = {
							type = industrial_complex
							level = 2
							instant_build = yes
						}
					}
				}
			}
			custom_effect_tooltip = RSC_Organizational_Industrial_effect
		}
		x = 12
		y = 6
	}
	focus = {
		id = RSC_Advanced_tank_tactics
		icon = GFX_focus_generic_army_tanks2
		cost = 10
		prerequisite = {
			focus = RSC_Impr_military_tech
		}
		completion_reward = {
			add_ideas = RSC_SDZ
		}
		x = 20
		y = 4
	}
	focus = {
		id = RSC_fly_machine
		icon = GFX_focus_generic_armored_air_support
		cost = 10
		prerequisite = {
			focus = RSC_Impr_military_tech
		}
		completion_reward = {
			add_ideas = RSC_TKZY
			add_tech_bonus = {
				bonus = 0.5
				uses = 2
				category = light_air
			}
		}
		x = 22
		y = 4
	}
	focus = {
		id = RSC_Inhouse_production
		icon = GFX_goal_generic_construct_mil_factory
		cost = 5
		prerequisite = {
			focus = RSC_fly_machine
		}
		prerequisite = {
			focus = RSC_Advanced_tank_tactics
		}
		completion_reward = {
			add_ideas = RSC_ZJZZ
			if = {
				limit = {
					has_war = yes
				}
				add_tech_bonus = {
					bonus = 0.5
					uses = 3
					category = industry
				}
			}
		}
		x = 21
		y = 5
	}
	focus = {
		id = RSC_Armed_line
		icon = GFX_goal_generic_build_tank
		cost = 10
		prerequisite = {
			focus = RSC_Inhouse_production
		}
		completion_reward = {
			add_ideas = RSC_WZDX
		}
		x = 21
		y = 6
	}
	focus = {
		id = RSC_FarEast_Petrograd
		icon = GFX_goal_generic_political_pressure
		cost = 5
		prerequisite = {
			focus = RSC_freedom_rus
		}
		available = {
			custom_override_tooltip = {#RSC_state_government_request
				tooltip = {localization_key = RSC_FarEast_Petrograd_tooltip}
				check_variable = {
					RSC_state_government_request > 2
				}
			}
		}
		completion_reward = {
			hidden_effect = {
				every_state = {
					if = {
						limit = {
							is_claimed_by = RSC
							not = {
								is_core_of = RSC
							}
						}
						add_core_of = RSC
					}
				}
			}
			clr_country_flag = overFDRUS6
			custom_effect_tooltip = rus_add_core_effect
			#unlock_decision_tooltip = rus_annex_state_government
			#unlock_decision_category_tooltip = RSC_FarEast_Petrograd_decision
		}
		x = 8
		y = 4
	}
	focus = {
		id = RSC_United_Tsar
		icon = GFX_goal_generic_forceful_treaty
		cost = 10
		prerequisite = {
			focus = RSC_FarEast_Petrograd
		}
		mutually_exclusive = {
			focus = RSC_United_Tsar_n
		}
		completion_reward = {
			#基于卡卓选择跳过毕苏斯基计划
			KAM = {
				country_event = {
					id = RSC_UT.1
					days = 3
				}
			}
			custom_effect_tooltip = RSC_United_Tsar_effect_y
		}
		x = 8
		y = 5
	}
	focus = {
		id = RSC_United_Tsar_n
		icon = GFX_goal_generic_forceful_treaty
		cost = 10
		prerequisite = {
			focus = RSC_FarEast_Petrograd
		}
		mutually_exclusive = {
			focus = RSC_United_Tsar
		}
		completion_reward = {
			#基于卡卓选择跳过毕苏斯基计划
			KAM = {
				country_event = {
					id = RSC_UT.5
					days = 3
				}
			}
			custom_effect_tooltip = RSC_United_Tsar_effect_yn
		}
		x = 6
		y = 5
	}
	focus = {
		id = RSC_Persuasion
		icon = GFX_goal_generic_national_unity
		cost = 10
		prerequisite = {
			focus = RSC_United_Tsar
			focus = RSC_United_Tsar_n
		}
		bypass = {
		}
		completion_reward = {
			add_ideas = RSC_TYDY
			custom_effect_tooltip = rus_persuasion_effect
			if = {
				limit = {
					KAM = {
						has_country_flag = UT_yes
					}
				}
				ROOT = {
					country_event = {
						id = RSC_UT.4
						days = 3
					}
				}
			}
		}
		x = 8
		y = 6
	}
	focus = {
		id = RSC_flag_fly
		icon = GFX_goal_support_democracy
		cost = 10
		prerequisite = {
			focus = RSC_everybodyRun
		}
		prerequisite = {
			focus = RSC_Persuasion
		}
		prerequisite = {
			focus = RSC_Armed_line
		}
		prerequisite = {
			focus = RSC_b2_5
			focus = RSC_b2_6
		}
		completion_reward = {
			add_ideas = RSC_ELSGJ
		}
		x = 15
		y = 7
	}
	focus = {
		id = RSC_small_space
		icon = GFX_focus_generic_agricultural_subsidies
		prerequisite = {
			focus = RSC_selling_tech
		}
		completion_reward = {
			205 = {
				if = {
					limit = {
						is_controlled_by = RSC
						is_core_of = RSC
					}
					add_extra_state_shared_building_slots = 1
					add_building_construction = {
						type = arms_factory
						level = 1
					}
				}
			}
			223 = {
				if = {
					limit = {
						is_controlled_by = RSC
						is_core_of = RSC
					}
					add_extra_state_shared_building_slots = 1
					add_building_construction = {
						type = arms_factory
						level = 1
					}
				}
			}
			222 = {
				if = {
					limit = {
						is_controlled_by = RSC
						is_core_of = RSC
					}
					add_extra_state_shared_building_slots = 1
					add_building_construction = {
						type = arms_factory
						level = 1
					}
				}
			}
			258 = {
				if = {
					limit = {
						is_controlled_by = RSC
						is_core_of = RSC
					}
					add_extra_state_shared_building_slots = 1
					add_building_construction = {
						type = arms_factory
						level = 1
					}
				}
			}
		}
		cost = 5
		x = 12
		y = 5
	}
	focus = {
		id = RSC_fly_to_space
		icon = GFX_focus_NORDIC_dem_research
		cost = 52.14
		x = 15
		y = 8
		prerequisite = {
			focus = RSC_flag_fly
		}
		completion_reward = {
			add_research_slot = 3
			add_ideas = RSC_fly_to_space_ideas
			dismantle_faction = yes
		}
	}
	focus = {
		id = RSC_b1		#俄罗斯经济建设计划
		icon = GFX_focus_AFG_establish_darulaman
		cost = 5
		x = 27
		y = 2
		prerequisite = {
			focus = RSC_SecondElection
		}
		completion_reward = {
			add_political_power = 100
		}
	}
	focus = {
		id = RSC_b2		#卡尔达舍夫伟业
		icon = GFX_focus_AFG_integrate_tajik_and_uzbek_republics
		cost = 5
		x = 15
		y = 3
		prerequisite = {
			focus = RSC_b1
		}
		prerequisite = {
			focus = RSC_KardashevRoad
		}
		completion_reward = {
			set_power_balance = {
				id = RSC_KDR_bop
				set_default = yes
			}
			custom_effect_tooltip = RSC_b2_effect			# 卡尔达舍夫伟业效果提示
			custom_effect_tooltip = RSC_WY_ttA			# 添加新的效果提示
			set_variable = {
				RSC_research_speed_factor = 0
			}
			set_variable = {
				RSC_drift_defence_factor = 0
			}
			set_variable = {
				RSC_political_power_factor = 0.05
			}
			set_variable = {
				RSC_industrial_capacity_factory = 0
			}
			set_variable = {
				RSC_consumer_goods_factor = 0
			}
			set_variable = {
				RSC_production_speed_buildings_factor = 0
			}
			hidden_effect = {
				add_dynamic_modifier = {
					modifier = RSC_KASDFWY
				}
			}
		}
	}
	focus = {
		id = RSC_b3		#科学国基建计划
		icon = GFX_focus_AUS_reclaim_habsburg
		cost = 5
		x = 35
		y = 3
		prerequisite = {
			focus = RSC_b1
		}
		completion_reward = {
			add_ideas = RSC_JJE
		}
	}
	focus = {
		id = RSC_b3_1		#西部基本基建
		icon = GFX_focus_AFG_contact_rural_loyalists
		cost = 5
		x = 33
		y = 4
		prerequisite = {
			focus = RSC_b3
		}
		completion_reward = {
			190 = {
				add_building_construction = {
					type = infrastructure
					level = 1
					instant_build = yes
				}
			}
			809 = {
				add_building_construction = {
					type = infrastructure
					level = 1
					instant_build = yes
				}
			}
			808 = {
				add_building_construction = {
					type = infrastructure
					level = 1
					instant_build = yes
				}
			}
			12 = {
				add_building_construction = {
					type = infrastructure
					level = 1
					instant_build = yes
				}
			}
			810 = {
				add_building_construction = {
					type = infrastructure
					level = 1
					instant_build = yes
				}
			}
			209 = {
				add_building_construction = {
					type = infrastructure
					level = 1
					instant_build = yes
				}
			}
			208 = {
				add_building_construction = {
					type = infrastructure
					level = 1
					instant_build = yes
				}
			}
			263 = {
				add_building_construction = {
					type = infrastructure
					level = 1
					instant_build = yes
				}
			}
			210 = {
				add_building_construction = {
					type = infrastructure
					level = 1
					instant_build = yes
				}
			}
		}
	}
	focus = {
		id = RSC_b3_2		#莫斯科大区基建
		icon = GFX_focus_BUL_destroy_the_zveno
		cost = 5
		x = 35
		y = 4
		prerequisite = {
			focus = RSC_b3
		}
		completion_reward = {
			219 = {
				add_building_construction = {
					type = infrastructure
					level = 1
					instant_build = yes
				}
			}
			246 = {
				add_building_construction = {
					type = infrastructure
					level = 1
					instant_build = yes
				}
			}
			247 = {
				add_building_construction = {
					type = infrastructure
					level = 1
					instant_build = yes
				}
			}
			248 = {
				add_building_construction = {
					type = infrastructure
					level = 1
					instant_build = yes
				}
			}
			253 = {
				add_building_construction = {
					type = infrastructure
					level = 1
					instant_build = yes
				}
			}
			254 = {
				add_building_construction = {
					type = infrastructure
					level = 1
					instant_build = yes
				}
			}
			223 = {
				add_building_construction = {
					type = infrastructure
					level = 1
					instant_build = yes
				}
			}
			205 = {
				add_building_construction = {
					type = infrastructure
					level = 1
					instant_build = yes
				}
			}
			242 = {
				add_building_construction = {
					type = infrastructure
					level = 1
					instant_build = yes
				}
			}
		}
	}
	focus = {
		id = RSC_b3_3		#东部基本基建
		icon = GFX_focus_AFG_border_guards
		cost = 5
		x = 37
		y = 4
		prerequisite = {
			focus = RSC_b3
		}
		completion_reward = {
			255 = {
				add_building_construction = {
					type = infrastructure
					level = 1
					instant_build = yes
				}
			}
			252 = {
				add_building_construction = {
					type = infrastructure
					level = 1
					instant_build = yes
				}
			}
			256 = {
				add_building_construction = {
					type = infrastructure
					level = 1
					instant_build = yes
				}
			}
			250 = {
				add_building_construction = {
					type = infrastructure
					level = 1
					instant_build = yes
				}
			}
			833 = {
				add_building_construction = {
					type = infrastructure
					level = 1
					instant_build = yes
				}
			}
		}
	}
	focus = {
		id = RSC_b3_4		#海边防御
		icon = GFX_focus_IRQ_construct_the_kut_barrage
		cost = 5
		x = 33
		y = 5
		prerequisite = {
			focus = RSC_b3_1
		}
		completion_reward = {
			#naval_base
			190 = {
				add_building_construction = {
					type = coastal_bunker
					level = 1
					province = {
						id = 6322
						id = 3296
						id = 3319
						id = 9262
					}
					instant_build = yes
				}
			}
			809 = {
				add_building_construction = {
					type = coastal_bunker
					level = 1
					province = {
						id = 3525
						id = 6222
					}
					instant_build = yes
				}
			}
			808 = {
				add_building_construction = {
					type = coastal_bunker
					level = 1
					province = {
						id = 9340
						id = 9240
					}
					instant_build = yes
				}
			}
			12 = {
				add_building_construction = {
					type = coastal_bunker
					level = 1
					province = {
						id = 9317
					}
					instant_build = yes
				}
			}
			208 = {
				add_building_construction = {
					type = coastal_bunker
					level = 1
					province = {
						id = 104
						id = 11186
						id = 9097
					}
					instant_build = yes
				}
			}
		}
	}
	focus = {
		id = RSC_b3_5		#莫斯科防御
		icon = GFX_focus_generic_the_giant_wakes
		cost = 5
		x = 35
		y = 5
		prerequisite = {
			focus = RSC_b3_2
		}
		completion_reward = {
			219 = {
				add_building_construction = {
					type = bunker
					level = 1
					province = {
						id = 6380
						id = 11282
						id = 3391
						id = 6414
						id = 301
						id = 9378
						id = 6283
						id = 3259
					}
					instant_build = yes
				}
			}
		}
	}
	focus = {
		id = RSC_b3_6		#东部督防
		icon = GFX_focus_CHL_the_future_of_Chile
		cost = 5
		x = 37
		y = 5
		prerequisite = {
			focus = RSC_b3_3
		}
		completion_reward = {
			573 = {
				add_building_construction = {
					type = supply_node
					level = 1
					province = 3280
					instant_build = yes
				}
			}
			build_railway = {
				path ={3280 10136 12117}
			}
		}
	}
	focus = {
		id = RSC_b3_7		#横贯大铁路
		icon = GFX_focus_CHL_domestic_production_of_the_willys_mb
		cost = 5
		x = 35
		y = 6
		prerequisite = {
			focus = RSC_b3_4
		}
		prerequisite = {
			focus = RSC_b3_5
		}
		prerequisite = {
			focus = RSC_b3_6
		}
		completion_reward = {
			build_railway = {
				#铺设铁路
				path = {
					9340
					10323
					7482
					341
					9275
					11259
					9386
					387
					6293
					354
					11272
					6400
					9399
					9272
					11254
					11269
					11300
					382
					3377
					6348
					6414
					6380
					301
					11362
					6316
					9311
					3370
					3268
					6392
					6295
					9389
					11375
				}
			}
		}
	}
	focus = {
		id = RSC_b2_1		#社会科技建设
		icon = GFX_focus_generic_socialist_science
		cost = 5
		x = 16
		y = 4
		prerequisite = {
			focus = RSC_b2
		}
		mutually_exclusive = {
			focus = RSC_b2_2
		}
		completion_reward = {
			add_power_balance_value = {
				id = RSC_KDR_bop
				value = 0.1
				tooltip_side = RSC_build_side
			}
			add_to_variable = {
				RSC_research_speed_factor = 0.1
			}
			add_to_variable = {
				RSC_consumer_goods_factor = 0.1
			}
			add_to_variable = {
				RSC_production_speed_buildings_factor = -0.1
			}
			add_to_variable = {
				RSC_industrial_capacity_factory = -0.1
			}
			custom_effect_tooltip = RSC_WY_tt
			custom_effect_tooltip = RSC_WY_tt1
			unlock_decision_tooltip = RSC_KDR_increase_science_middle
		}
	}
	focus = {
		id = RSC_b2_2		#建设委员会
		icon = GFX_focus_ITA_il_sol_dell_avvenire
		cost = 5
		x = 18
		y = 4
		prerequisite = {
			focus = RSC_b2
		}
		mutually_exclusive = {
			focus = RSC_b2_1
		}
		completion_reward = {
			add_power_balance_value = {
				id = RSC_KDR_bop
				value = -0.1
				tooltip_side = RSC_build_side
			}
			add_to_variable = {
				RSC_research_speed_factor = -0.1
			}
			add_to_variable = {
				RSC_consumer_goods_factor = -0.1
			}
			add_to_variable = {
				RSC_production_speed_buildings_factor = 0.1
			}
			add_to_variable = {
				RSC_industrial_capacity_factory = 0.1
			}
			custom_effect_tooltip = RSC_WY_tt
			custom_effect_tooltip = RSC_WY_tt2
			unlock_decision_tooltip = RSC_KDR_increase_build_middle
		}
	}
	focus = {
		id = RSC_b2_3		#强制计划
		icon = GFX_focus_NORDIC_dem_capstone_civilian
		cost = 5
		x = 16
		y = 5
		prerequisite = {
			focus = RSC_b2_1
			focus = RSC_b2_2
		}
		prerequisite = {
			focus = RSC_selling_tech
		}
		mutually_exclusive = {
			focus = RSC_b2_4
		}
		completion_reward = {
			add_power_balance_value = {
				id = RSC_KDR_bop
				value = 0.15
				tooltip_side = RSC_science_side
			}
			add_to_variable = {
				RSC_research_speed_factor = 0.1
			}
			add_to_variable = {
				RSC_consumer_goods_factor = 0.1
			}
			add_to_variable = {
				RSC_production_speed_buildings_factor = -0.1
			}
			add_to_variable = {
				RSC_industrial_capacity_factory = -0.1
			}
			custom_effect_tooltip = RSC_WY_tt
			custom_effect_tooltip = RSC_WY_tt1
			unlock_decision_tooltip = RSC_KDR_increase_science_big
		}
	}
	focus = {
		id = RSC_b2_4		#自由化建设
		icon = GFX_focus_NORDIC_dem_selector_civilian
		cost = 5
		x = 18
		y = 5
		prerequisite = {
			focus = RSC_b2_1
			focus = RSC_b2_2
		}
		prerequisite = {
			focus = RSC_selling_tech
		}
		mutually_exclusive = {
			focus = RSC_b2_3
		}
		completion_reward = {
			add_power_balance_value = {
				id = RSC_KDR_bop
				value = -0.15
				tooltip_side = RSC_build_side
			}
			add_to_variable = {
				RSC_research_speed_factor = -0.1
			}
			add_to_variable = {
				RSC_consumer_goods_factor = -0.1
			}
			add_to_variable = {
				RSC_production_speed_buildings_factor = 0.1
			}
			add_to_variable = {
				RSC_industrial_capacity_factory = 0.1
			}
			custom_effect_tooltip = RSC_WY_tt
			custom_effect_tooltip = RSC_WY_tt2
			unlock_decision_tooltip = RSC_KDR_increase_build_big
		}
	}
	focus = {
		id = RSC_b2_5		#建设计划制
		icon = GFX_focus_AFG_a_modern_economy
		cost = 5
		x = 16
		y = 6
		prerequisite = {
			focus = RSC_b2_3
			focus = RSC_b2_4
		}
		mutually_exclusive = {
			focus = RSC_b2_6
		}
		completion_reward = {
			add_power_balance_value = {
				id = RSC_KDR_bop
				value = 0.2
				tooltip_side = RSC_build_side
			}
			add_to_variable = {
				RSC_research_speed_factor = 0.1
			}
			add_to_variable = {
				RSC_consumer_goods_factor = 0.1
			}
			add_to_variable = {
				RSC_production_speed_buildings_factor = -0.1
			}
			add_to_variable = {
				RSC_industrial_capacity_factory = -0.1
			}
			custom_effect_tooltip = RSC_WY_tt
			custom_effect_tooltip = RSC_WY_tt1
			unlock_decision_tooltip = RSC_KDR_increase_build_super
		}
	}
	focus = {
		id = RSC_b2_6		#市场与计划经济
		icon = GFX_focus_generic_land_reclamation
		cost = 5
		x = 18
		y = 6
		prerequisite = {
			focus = RSC_b2_3
			focus = RSC_b2_4
		}
		mutually_exclusive = {
			focus = RSC_b2_5
		}
		completion_reward = {
			add_power_balance_value = {
				id = RSC_KDR_bop
				value = -0.2
				tooltip_side = RSC_science_side
			}
			add_to_variable = {
				RSC_research_speed_factor = -0.1
			}
			add_to_variable = {
				RSC_consumer_goods_factor = -0.1
			}
			add_to_variable = {
				RSC_production_speed_buildings_factor = 0.1
			}
			add_to_variable = {
				RSC_industrial_capacity_factory = 0.1
			}
			custom_effect_tooltip = RSC_WY_tt
			custom_effect_tooltip = RSC_WY_tt2
			unlock_decision_tooltip = RSC_KDR_increase_science_super
		}
	}
	focus = {
		id = RSC_b2_7		#接手商业经济
		icon = GFX_focus_IRQ_decouple_from_the_pound
		cost = 5
		x = 14
		y = 5
		prerequisite = {
			focus = RSC_b2_1
			focus = RSC_selling_tech
			focus = RSC_b2_2
		}
		completion_reward = {
			add_to_variable = {
				RSC_consumer_goods_factor = -0.05
			}
			add_power_balance_value = {
				id = RSC_KDR_bop
				value = -0.5
				tooltip_side = RSC_build_side
			}
			custom_effect_tooltip = RSC_WY_tt
			custom_effect_tooltip = RSC_WY_tt3
		}
	}
	focus = {
		id = RSC_b4		#开展军事建设
		icon = GFX_focus_AFG_against_kabul
		cost = 5
		x = 25
		y = 3
		prerequisite = {
			focus = RSC_b1
		}
		completion_reward = {
			custom_effect_tooltip = RSC_b4_tt5
			set_variable = {
				RSC_army_attack_factor = 0.05
			}
			set_variable = {
				RSC_army_defence_factor = 0.05
			}
			set_variable = {
				RSC_army_speed_factor = 0.05
			}
			set_variable = {
				RSC_garrison_attack_factor = 0.05
			}
			set_variable = {
				RSC_garrison_defence_factor = 0.05
			}
			set_variable = {
				RSC_garrison_speed_factor = 0.05
			}
			set_variable = {
				RSC_consumer_goods_factor_1 = 0
			}
			hidden_effect = {
				add_dynamic_modifier = {
					modifier = RSC_LJJH
				}
			}
		}
	}
	focus = {
		id = RSC_b4_1		#建立国有军企
		icon = GFX_focus_AFG_scavenging
		cost = 5
		x = 25
		y = 4
		prerequisite = {
			focus = RSC_b4
		}
		completion_reward = {
			add_to_variable = {
				RSC_army_attack_factor = 0.05
			}
			add_to_variable = {
				RSC_army_defence_factor = 0.05
			}
			custom_effect_tooltip = RSC_b4_tt
			custom_effect_tooltip = RSC_b4_tt1
			add_mio_funding = {
				target = rsc_l_1_n
				amount = 500
			}
		}
	}
	focus = {
		id = RSC_b4_2		#增加军队经费
		icon = GFX_focus_ARG_chilean_ultimatum
		cost = 5
		x = 23
		y = 5
		prerequisite = {
			focus = RSC_b4_1
		}
		completion_reward = {
			add_to_variable = {
				RSC_army_attack_factor = 0.05
			}
			add_to_variable = {
				RSC_army_defence_factor = 0.05
			}
			add_to_variable = {
				RSC_army_speed_factor = 0.05
			}
			custom_effect_tooltip = RSC_b4_tt
			custom_effect_tooltip = RSC_b4_tt2
		}
	}
	focus = {
		id = RSC_b4_3		#引入大国资本
		icon = GFX_focus_ARG_crackdown_on_corruption
		cost = 5
		x = 25
		y = 5
		prerequisite = {
			focus = RSC_b4_1
		}
		completion_reward = {
			190 = {
				if = {
					limit = {
						is_controlled_by = RSC
						is_core_of = RSC
					}
					add_extra_state_shared_building_slots = 1
					add_building_construction = {
						type = arms_factory
						level = 1
					}
				}
			}
			809 = {
				if = {
					limit = {
						is_controlled_by = RSC
						is_core_of = RSC
					}
					add_extra_state_shared_building_slots = 1
					add_building_construction = {
						type = arms_factory
						level = 1
					}
				}
			}
			808 = {
				if = {
					limit = {
						is_controlled_by = RSC
						is_core_of = RSC
					}
					add_extra_state_shared_building_slots = 1
					add_building_construction = {
						type = arms_factory
						level = 1
					}
				}
			}
			12 = {
				if = {
					limit = {
						is_controlled_by = RSC
						is_core_of = RSC
					}
					add_extra_state_shared_building_slots = 1
					add_building_construction = {
						type = arms_factory
						level = 1
					}
				}
			}
			add_to_variable = {
				RSC_army_speed_factor = -0.05
			}
			add_to_variable = {
				RSC_consumer_goods_factor_1 = -0.05
			}
			custom_effect_tooltip = RSC_b4_tt
			custom_effect_tooltip = RSC_b4_tt3
		}
	}
	focus = {
		id = RSC_b4_4		#开放民众生产
		icon = GFX_focus_ARG_capitalize_the_beef_industry
		cost = 5
		x = 27
		y = 5
		prerequisite = {
			focus = RSC_b4_1
			focus = RSC_b5_1
		}
		completion_reward = {
			add_ideas = RSC_KF
		}
	}
	focus = {
		id = RSC_b4_5		#创新军事科技
		icon = GFX_focus_ARG_develop_the_electricity_sector
		cost = 5
		x = 23
		y = 6
		prerequisite = {
			focus = RSC_b4_2
		}
		completion_reward = {
			add_tech_bonus = {
				bonus = 1
				uses = 1
				category = artillery
			}
			add_tech_bonus = {
				bonus = 1
				uses = 1
				category = artillery
			}
			add_tech_bonus = {
				bonus = 1
				uses = 1
				category = artillery
			}
		}
		x = 23
		y = 6
	}
	focus = {
		id = RSC_b4_6		#引入劳动力
		icon = GFX_focus_ARG_women_in_industry
		cost = 5
		x = 25
		y = 6
		prerequisite = {
			focus = RSC_b4_3
		}
		completion_reward = {
			12 = {
				add_manpower = 100000
			}
			190 = {
				add_manpower = 100000
			}
			809 = {
				add_manpower = 100000
			}
			808 = {
				add_manpower = 100000
			}
			add_to_variable = {
				RSC_garrison_attack_factor = 0.05
			}
			add_to_variable = {
				RSC_garrison_defence_factor = 0.05
			}
			add_to_variable = {
				RSC_garrison_speed_factor = 0.05
			}
			custom_effect_tooltip = RSC_b4_tt
			custom_effect_tooltip = RSC_b4_tt4
		}
	}
	focus = {
		id = RSC_b5
		icon = GFX_focus_eng_concessions_to_the_trade_unions
		cost = 5
		x = 29
		y = 3
		prerequisite = {
			focus = RSC_b1
		}
		completion_reward = {
			219 = {
				if = {
					limit = {
						is_controlled_by = RSC
						is_core_of = RSC
					}
					add_extra_state_shared_building_slots = 1
					add_building_construction = {
						type = industrial_complex
						level = 1
					}
				}
			}
			205 = {
				if = {
					limit = {
						is_controlled_by = RSC
						is_core_of = RSC
					}
					add_extra_state_shared_building_slots = 1
					add_building_construction = {
						type = industrial_complex
						level = 1
					}
				}
			}
			223 = {
				if = {
					limit = {
						is_controlled_by = RSC
						is_core_of = RSC
					}
					add_extra_state_shared_building_slots = 1
					add_building_construction = {
						type = industrial_complex
						level = 1
					}
				}
			}
			253 = {
				if = {
					limit = {
						is_controlled_by = RSC
						is_core_of = RSC
					}
					add_extra_state_shared_building_slots = 1
					add_building_construction = {
						type = industrial_complex
						level = 1
					}
				}
			}
			254 = {
				if = {
					limit = {
						is_controlled_by = RSC
						is_core_of = RSC
					}
					add_extra_state_shared_building_slots = 1
					add_building_construction = {
						type = industrial_complex
						level = 1
					}
				}
			}
			add_timed_idea = {
				idea = RSC_KK
				days = 120
			}
		}
	}
	focus = {
		id = RSC_b5_1		#消费品改革
		icon = GFX_focus_FIN_finnish_federation_of_trade_unions
		cost = 5
		x = 29
		y = 4
		prerequisite = {
			focus = RSC_b5
		}
		completion_reward = {
			add_ideas = RSC_JJ1
		}
	}
	focus = {
		id = RSC_b5_2		#劳动教育
		icon = GFX_focus_FIN_socialist_welfare
		cost = 5
		x = 29
		y = 5
		prerequisite = {
			focus = RSC_b5_1
		}
		completion_reward = {
			add_ideas = RSC_JJ2
		}
	}
	focus = {
		id = RSC_b5_3		#合作社生产
		icon = GFX_focus_generic_social_democracy
		cost = 5
		x = 31
		y = 5
		prerequisite = {
			focus = RSC_b5_1
		}
		completion_reward = {
			add_ideas = RSC_JJ4
		}
	}
	focus = {
		id = RSC_b5_4		#教育产生革新
		icon = GFX_focus_generic_socialist_science
		cost = 5
		x = 29
		y = 6
		prerequisite = {
			focus = RSC_b5_2
		}
		completion_reward = {
			swap_ideas = {
				remove_idea = RSC_JJ2
				add_idea = RSC_JJ3
			}
		}
	}
	focus = {
		id = RSC_b5_5		#人民公社
		icon = GFX_focus_generic_supply_line
		cost = 5
		x = 31
		y = 6
		prerequisite = {
			focus = RSC_b5_3
		}
		completion_reward = {
			swap_ideas = {
				remove_idea = RSC_JJ4
				add_idea = RSC_JJ5
			}
		}
	}
}
