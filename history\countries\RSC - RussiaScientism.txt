capital = 219
set_research_slots = 4
recruit_character = RSC_nigorla
set_politics = {
	ruling_party = neutrality
	last_election = "1935.12.1"
	election_frequency = 48
	elections_allowed = no #是否选举
}
set_oob = RSC_arm
set_popularities = {
	democratic = 10
	communism = 5
	neutrality = 85
}

add_ideas = {
	limited_conscription #限制征兵
}
add_dynamic_modifier = {
	modifier = RSC_Technocracy
}
set_technology = {
	aa_lmg = 1
	engines_1 = 1
	engines_2 = 1
	early_bombs = 1
	aircraft_construction = 1
	iw_small_airframe = 1
	iw_medium_airframe = 1
	iw_large_airframe = 1
	basic_medium_airframe = 1
	basic_small_airframe = 1
	air_torpedoe_1 = 1
	bba_early_transport_plane = 1
}
set_variable = {
	RSC_Technocracy_production_factory_start_efficiency_factor = 0.05
}
set_variable = {
	RSC_Technocracy_stability_factor = 0.1
}
set_variable = {
	RSC_Technocracy_research_speed_factor = 0.1
}
set_variable = {
	RSC_Technocracy_conscription_factor = 0.05
}
set_war_support = 0.2
set_stability = 0.6
create_equipment_variant = {
	name = "玛·贾·琪 I"
	type = light_tank_chassis_1   #（有man the gun dlc的情况下，海军设计必须使用专门的海军类型，见下面）
	parent_version = 0
	# name_group = 名称组（可不写）
	modules = {
		main_armament_slot = tank_heavy_machine_gun
		turret_type_slot = tank_light_one_man_tank_turret
		suspension_type_slot = tank_christie_suspension
		armor_type_slot = tank_welded_armor
		engine_type_slot = tank_gasoline_engine
	}
	upgrades = {
		tank_nsb_engine_upgrade = 2
		tank_nsb_armor_upgrade = 2
	}
	icon = "GFX_ENG_basic_light_tank_medium"
	obsolete = yes
}
create_equipment_variant = {
	name = "玛·贾·琪 II"
	type = light_tank_chassis_2   #（有man the gun dlc的情况下，海军设计必须使用专门的海军类型，见下面）
	parent_version = 0
	# name_group = 名称组（可不写）
	modules = {
		main_armament_slot = tank_heavy_machine_gun
		turret_type_slot = tank_light_one_man_tank_turret
		suspension_type_slot = tank_christie_suspension
		armor_type_slot = tank_welded_armor
		engine_type_slot = tank_gasoline_engine
	}
	upgrades = {
		tank_nsb_engine_upgrade = 4
		tank_nsb_armor_upgrade = 4
	}
	icon = "GFX_ENG_basic_light_tank_medium"
}