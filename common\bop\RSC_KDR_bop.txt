RSC_KDR_bop = {
	initial_value = 0.5
	left_side = RSC_build_side
	right_side = RSC_science_side
	decision_category = RSC_KDR_bop_category
	
	# 中间范围 (50% 阈值)
	range = {
		id = mid_range
		min = -0.25
		max = 0.25
		modifier = {
			research_speed_factor = 0.05
			production_speed_buildings_factor = 0.05
			stability_factor = 0.10
		}
	}
	
	# 科学侧 (Science)
	side = {
		id = RSC_science_side
		icon = GFX_bop_ITA_balbo_side
		
		# 25% 阈值
		range = {
			id = science_range_25
			min = 0.25
			max = 0.50
			modifier = {
				research_speed_factor = 0.10
				production_speed_buildings_factor = -0.05
				political_power_gain = 0.05
			}
		}
		
		# 50% 阈值
		range = {
			id = science_range_50
			min = 0.50
			max = 0.75
			modifier = {
				research_speed_factor = 0.15
				production_speed_buildings_factor = -0.10
				political_power_gain = 0.10
				stability_factor = -0.05
			}
		}
		
		# 75% 阈值
		range = {
			id = science_range_75
			min = 0.75
			max = 0.95
			modifier = {
				research_speed_factor = 0.20
				production_speed_buildings_factor = -0.15
				political_power_gain = 0.15
				stability_factor = -0.10
			}
		}
		
		# 95% 阈值
		range = {
			id = science_range_95
			min = 0.95
			max = 1.0
			on_activate = {
				custom_effect_tooltip = science_range_tooltip
				hidden_effect = {	
					country_event = RSC_kexueyuan.1
				}
			}
		}
	}
	
	# 建设侧 (Build)
	side = {
		id = RSC_build_side
		icon = GFX_bop_ITA_grand_council_side
		
		# 25% 阈值
		range = {
			id = build_range_25
			min = -0.50
			max = -0.25
			modifier = {
				research_speed_factor = -0.05
				production_speed_buildings_factor = 0.10
				political_power_gain = -0.05
			}
		}
		
		# 50% 阈值
		range = {
			id = build_range_50
			min = -0.75
			max = -0.50
			modifier = {
				research_speed_factor = -0.10
				production_speed_buildings_factor = 0.15
				political_power_gain = -0.10
				resources_factor = 0.10
			}
		}
		
		# 75% 阈值
		range = {
			id = build_range_75
			min = -0.95
			max = -0.75
			modifier = {
				research_speed_factor = -0.15
				production_speed_buildings_factor = 0.20
				political_power_gain = -0.15
				resources_factor = 0.15
				global_building_slots_factor = 0.10
			}
		}
		
		# 95% 阈值
		range = {
			id = build_range_95
			min = -1.0
			max = -0.95
			on_activate = {
				hidden_effect = {
					country_event = RSC_mosikebengkui.1
				}
				custom_effect_tooltip = build_range_tooltip
			}
		}
	}
} 