add_namespace = RSC_a
add_namespace = RSC_b
add_namespace = RSC_b_t
add_namespace = RSC_b_f
add_namespace = Rus_puppet_state_government
add_namespace = give_annex_tooltip_e
add_namespace = RSC_UT
add_namespace = rsc_flag
add_namespace = RSC_mosikebengkui
add_namespace = RSC_kexueyuan
add_namespace = RYH1
country_event = {
	id = RSC_a.1
	title = RSC_a.1.t
	desc = RSC_a.1.d
	#picture = 
	is_triggered_only = yes
	option = {
		name = RSC_a.1.a
		ai_chance = {
			base = 100
		}
		country_event = {
			id = RSC_a.2
			days = 30
		}
	}
}

#选举
country_event = {
	id = RSC_a.2
	title = RSC_a.2.t
	desc = RSC_a.2.d
	#picture = 
	is_triggered_only = yes
	option = {
		name = RSC_a.2.a
		ai_chance = {
			base = 100
		}
		complete_national_focus = RSC_KardashevRoad
	}
	option = {
		name = RSC_a.2.b
		ai_chance = {
			base = 0
		}
		country_event = {
			id = RSC_a.2
			days = 30
		}
	}
}

#选举1
country_event = {
	id = RSC_b.1
	title = RSC_b.1.t
	desc = RSC_b.1.d
	#picture = 
	is_triggered_only = yes
	option = {
		name = RSC_b.1.a
		ai_chance = {
			base = 50
		}
		trigger = {
			ROOT = {
				has_war = no
			}
		}
		ROOT = {
			add_tech_bonus = {
				bonus = 0.25
				uses = 1
				category = industry
			}
			add_tech_bonus = {
				bonus = 0.25
				uses = 1
				category = electronics
			}
			add_tech_bonus = {
				bonus = 0.25
				uses = 1
				category = infantry_weapons
			}
			add_timed_idea = {
				idea = get_tech
				days = 30
			}
		}
		RSC = {
			country_event = {
				id = RSC_b_t.1
				days = 3
			}
		}
	}
	option = {
		name = RSC_b.1.b
		ai_chance = {
			base = 50
		}
		RSC = {
			country_event = {
				id = RSC_b_f.1
				days = 3
			}
		}
	}
}

#技术1
country_event = {
	id = RSC_b.2
	title = RSC_b.2.t
	desc = RSC_b.2.d
	#picture = 
	is_triggered_only = yes
	option = {
		name = RSC_b.2.a
		ai_chance = {
			base = 50
		}
		trigger = {
			ROOT = {
				has_war = no
			}
		}
		ROOT = {
			add_tech_bonus = {
				bonus = 0.5
				uses = 1
				category = industry
			}
			add_tech_bonus = {
				bonus = 0.5
				uses = 1
				category = electronics
			}
			add_tech_bonus = {
				bonus = 0.5
				uses = 1
				category = infantry_weapons
			}
			remove_ideas = get_tech
			add_ideas = imp_hz
		}
		RSC = {
			country_event = {
				id = RSC_b_t.2
				days = 3
			}
		}
	}
}

#技术2
country_event = {
	id = RSC_b.3
	title = RSC_b.3.t
	desc = RSC_b.3.d
	#picture = 
	is_triggered_only = yes
	option = {
		name = RSC_b.1.a
		ai_chance = {
			base = 70
		}
		trigger = {
			ROOT = {
				has_war = no
			}
		}
		RSC = {
			country_event = {
				id = RSC_b_t.3
				days = 3
			}
		}
	}
	option = {
		name = RSC_b.3.b
		ai_chance = {
			base = 30
		}
		RSC = {
			country_event = {
				id = RSC_b_f.3
				days = 3
			}
		}
	}
}

#技术3
country_event = {
	id = RSC_b_t.1
	title = RSC_b_t.1.t
	desc = RSC_b_t.1.d
	#picture = 
	is_triggered_only = yes
	option = {
		name = RSC_b_t.1.a
		ai_chance = {
			base = 100
		}
		ROOT = {
			add_timed_idea = {
				idea = selling_tech
				days = 30
			}
		}
	}
}

#技术1同意
country_event = {
	id = RSC_b_f.1
	title = RSC_b_f.1.t
	desc = RSC_b_f.1.d
	#picture = 
	is_triggered_only = yes
	option = {
		name = RSC_b_t.1.a
		ai_chance = {
			base = 100
		}
	}
}

#技术1不同意
country_event = {
	id = RSC_b_t.2
	title = RSC_b_t.2.t
	desc = RSC_b_t.2.d
	#picture = 
	is_triggered_only = yes
	option = {
		name = RSC_b_t.1.a
		ai_chance = {
			base = 100
		}
		add_offsite_building = {
			type = arms_factory
			level = 1
		}
		add_offsite_building = {
			type = industrial_complex
			level = 1
		}
	}
}

#技术2通知
country_event = {
	id = RSC_b_t.3
	title = RSC_b_t.3.t
	desc = RSC_b_t.3.d
	#picture = 
	is_triggered_only = yes
	option = {
		name = RSC_b_t.1.a
		ai_chance = {
			base = 100
		}
		custom_effect_tooltip = add_var_RSC_techno
		ROOT = {
			add_to_variable = {
				RSC_Technocracy_research_speed_factor = 0.05
			}
		}
	}
}

#技术3同意
country_event = {
	id = RSC_b_f.3
	title = RSC_b_f.3.t
	desc = RSC_b_f.3.d
	#picture = 
	is_triggered_only = yes
	option = {
		name = RSC_b_t.1.a
		ai_chance = {
			base = 100
		}
	}
}

#技术3不同意
country_event = {
	id = Rus_puppet_state_government.1
	title = Rus_puppet_state_government.1.t
	desc = Rus_puppet_state_government.1.d
	#picture = 
	is_triggered_only = yes
	option = {
		name = Rus_puppet_state_government.1.a
		ai_chance = {
			base = 5
			modifier = {
				factor = 2
				has_army_size = {
					size < 24
				}
			}
			modifier = {
				factor = 1.5
				ROOT = {
					has_opinion = {
						target = FROM
						value > 60
					}
				}
			}
			modifier = {
				factor = 50
				ROOT = {
					is_in_faction_with = FROM
				}
			}
			modifier = {
				factor = 2
				FROM = {
					has_army_size = {
						size > 24
					}
				}
			}
		}
		FROM = {
			set_autonomy = {
				target = ROOT
				autonomy_state = autonomy_state_government
			}
		}
		ROOT = {
			set_country_flag = rus_state_government
		}
	}
	option = {
		name = Rus_puppet_state_government.1.b
		ai_chance = {
			base = 10
			modifier = {
				factor = 2
				has_army_size = {
					size > 24
				}
			}
			modifier = {
				factor = 1.5
				ROOT = {
					has_opinion = {
						target = FROM
						value < 60
					}
				}
			}
		}
		FROM = {
			# create_wargoal = {
			# 	target = ROOT
			# 	type = puppet_wargoal_focus
			# }
		}
		ROOT = {
			every_state = {
				if = {
					limit = {
						is_owned_by = ROOT
						not = {
							is_claimed_by = FROM
						}
					}
					add_claim_by = FROM
				}
			}
			set_country_flag = rus_not_state_government
		}
	}
}

#注释
country_event = {
	id = give_annex_tooltip_e.1
	title = give_annex_tooltip_e.1.t
	desc = give_annex_tooltip_e.1.d
	#picture = 
	is_triggered_only = yes
	option = {
		name = give_annex_tooltip_e.1.a
		ai_chance = {
			factor = 5
			modifier = {
				factor = 2
				has_army_size = {
					size < 24
				}
			}
			modifier = {
				factor = 1.5
				ROOT = {
					has_opinion = {
						target = FROM
						value > 60
					}
				}
			}
			modifier = {
				factor = 50
				ROOT = {
					is_in_faction_with = FROM
				}
			}
			modifier = {
				factor = 2
				FROM = {
					has_army_size = {
						size > 24
					}
				}
			}
		}
		FROM = {
			annex_country = {
				target = ROOT
				transfer_troops = yes
			}
		}
	}
	option = {
		name = give_annex_tooltip_e.1.b
		ai_chance = {
			factor = 10
			modifier = {
				factor = 2
				has_army_size = {
					size > 24
				}
			}
			modifier = {
				factor = 1.5
				ROOT = {
					has_opinion = {
						target = FROM
						value < 60
					}
				}
			}
		}
		FROM = {
			create_wargoal = {
				target = ROOT
				type = puppet_wargoal_focus
			}
		}
	}
}

#注释
country_event = {
	id = RSC_UT.1
	title = RSC_UT.1.t
	desc = RSC_UT.1.d
	#picture = 
	is_triggered_only = yes
	option = {
		name = RSC_UT.1.a
		ai_chance = {
			base = 50
		}
		add_to_faction = RSC
		ROOT = {
			set_country_flag = UT_yes
			set_country_flag = old_UT
		}
		FROM = {
			country_event = {
				id = RSC_UT.2
				days = 1
			}
		}
	}
	option = {
		name = RSC_b.1.b
		ai_chance = {
			base = 50
		}
		FROM = {
			country_event = {
				id = RSC_UT.3
				days = 1
			}
		}
	}
}

country_event = {
	id = RSC_UT.2
	title = RSC_UT.2.t
	desc = RSC_UT.2.d
	#picture = 
	is_triggered_only = yes
	option = {
		name = RSC_b_t.1.a
		ai_chance = {
			base = 50
		}
	}
}

country_event = {
	id = RSC_UT.3
	title = RSC_UT.3.t
	desc = RSC_UT.3.d
	#picture = 
	is_triggered_only = yes
	option = {
		name = RSC_b_t.1.a
		ai_chance = {
			base = 50
		}
	}
}

country_event = {
	id = RSC_UT.4
	title = RSC_UT.4.t
	desc = RSC_UT.4.d
	#picture = 
	is_triggered_only = yes
	option = {
		name = RSC_UT.4.b
		ai_chance = {
			base = 50
		}
		diplomatic_relation = {
			country = FROM
			relation = non_aggression_pact
			active = no
		}
		KAM = {
			leave_faction = yes
			create_wargoal = {
				target = ROOT
				type = take_claimed_state
			}
			add_state_claim = 809
			add_state_claim = 808
			add_state_claim = 12
			add_state_claim = 810
			add_state_claim = 190
			add_state_claim = 241
		}
		FROM = {
			clr_country_flag = UT_yes
		}
	}
	option = {
		name = RSC_UT.4.a
		ai_chance = {
			base = 50
		}
		KAM = {
			add_state_core = 809
			add_state_core = 808
			add_state_core = 12
			add_state_core = 810
			add_state_core = 190
			add_state_core = 241
			transfer_state = 809
			transfer_state = 808
			transfer_state = 12
			transfer_state = 810
			transfer_state = 190
			transfer_state = 241
		}
	}
}

country_event = {
	id = RSC_UT.5
	title = RSC_UT.5.t
	desc = RSC_UT.5.d
	#picture = 
	is_triggered_only = yes
	option = {
		name = RSC_b_t.5.a
		ai_chance = {
			base = 50
		}
		diplomatic_relation = {
			country = FROM
			relation = non_aggression_pact
		}
		RSC = {
			country_event = {
				id = RSC_UT.6
				days = 3
			}
		}
		ROOT = {
			transfer_state = 241
			add_state_core = 241
		}
	}
	option = {
		name = RSC_b.1.b
		ai_chance = {
			base = 50
		}
		RSC = {
			country_event = {
				id = RSC_UT.7
				days = 3
			}
		}
	}
}

country_event = {
	id = RSC_UT.6
	title = RSC_UT.6.t
	desc = RSC_UT.6.d
	#picture = 
	is_triggered_only = yes
	option = {
		name = RSC_b_t.1.a
		ai_chance = {
			base = 50
		}
	}
}

country_event = {
	id = RSC_UT.7
	title = RSC_UT.7.t
	desc = RSC_UT.7.d
	#picture = 
	is_triggered_only = yes
	option = {
		name = RSC_b_t.1.a
		ai_chance = {
			base = 50
		}
		diplomatic_relation = {
			country = FROM
			relation = non_aggression_pact
			active = no
		}
	}
}

country_event = {
	id = RSC_UT.9
	title = RSC_UT.9.t
	desc = RSC_UT.9.d
	#picture = 
	is_triggered_only = yes
	option = {
		name = RSC_UT.9.a
		ai_chance = {
			base = 100
		}
		add_ideas = RSC_PH
	}
}

country_event = {
	id = RSC_mosikebengkui.1
	title = RSC_mosikebengkui.1.t
	desc = RSC_mosikebengkui.1.d
	#picture = 
	is_triggered_only = yes
	option = {
		name = RSC_mosikebengkui.1.a
		ai_chance = {
			base = 50
		}
		RSC = {
			add_ideas = RSC_mosikebengkui_1
			country_event = {
				id = RSC_mosikebengkui.2
				days = 30
			}
		}
	}
}

country_event = {
	id = RSC_mosikebengkui.2
	title = RSC_mosikebengkui.2.t
	desc = RSC_mosikebengkui.2.d
	#picture = 
	is_triggered_only = yes
	option = {
		name = RSC_mosikebengkui.2.a
		ai_chance = {
			base = 50
		}
		RSC = {
			add_ideas = RSC_mosikebengkui_2
			add_ideas = RSC_mosikebengkui_3
			country_event = {
				id = RSC_mosikebengkui.3
				days = 30
			}
		}
		hidden_effect = {
		}
	}
}

country_event = {
	id = RSC_mosikebengkui.3
	title = RSC_mosikebengkui.3.t
	desc = RSC_mosikebengkui.3.d
	#picture = 
	is_triggered_only = yes
	option = {
		name = RSC_mosikebengkui.3.a
		ai_chance = {
			base = 50
		}
		RYH = {
			transfer_state = 219
			change_tag_from = RSC
			add_state_core = 219
			RSC = {
				every_unit_leader = {
					set_nationality = RYH
				}
			}
		}
	}
}

country_event = {
	id = RSC_kexueyuan.1
	title = RSC_kexueyuan.1.t
	desc = RSC_kexueyuan.1.d
	#picture = 
	is_triggered_only = yes
	option = {
		name = RSC_kexueyuan.1.a
		ai_chance = {
			base = 50
		}
		RSC = {
			retire_country_leader = yes
			create_country_leader = {
				ideology = stalinism
				picture = GFX_LEADER_RSC_YH
				expire = "1965.1.1"
				name = "科学院"
			}
			remove_power_balance = {
				id = RSC_KDR_bop
			}
		}
	}
}

country_event = {
	id = RYH1.1
	title = RYH1.1.t
	desc = RYH1.1.d
	#picture = 
	is_triggered_only = yes
	option = {
		name = RYH1.1.a
		ai_chance = {
			base = 50
		}
		set_party_name = {
			ideology = extremism
			long_name = "全俄罗斯统一党(激进派)"
			name = "全俄罗斯统一党(激进派)"
		}
		set_party_name = {
			ideology = fascism
			long_name = "全俄罗斯统一党"
			name = "全俄罗斯统一党"
		}
		set_party_name = {
			ideology = communism
			long_name = "新左翼社会革命党"
			name = "左翼社会革命党"
		}
		set_party_name = {
			ideology = democratic
			long_name = "新十月党"
			name = "新十月党"
		}
		set_party_name = {
			ideology = neutrality
			long_name = "临时议会"
			name = "临时议会"
		}
		# annex_country = {
		# 	target = RSC
		# 	transfer_troops = no
		# }
		custom_effect_tooltip = RYH11_tooltip
		hidden_effect = {
			country_event = {
				id = RYH1.2
				days = 30
			}
		}
	}
}

country_event = {
	id = RYH1.2
	title = RYH1.2.t
	desc = RYH1.2.d
	#picture = 
	is_triggered_only = yes
	option = {
		name = RYH1.2.a
		ai_chance = {
			base = 25
		}
		set_party_name = {
			ideology = neutrality
			long_name = "议会总部"
			name = "议会总部"
		}
		set_politics = {
			ruling_party = democratic
			elections_allowed = no
		}
		set_popularities = {
			democratic = 80
			communism = 5
			neutrality = 5
			fascism = 5
			extremism = 5
		}
		kill_country_leader = yes
		create_country_leader = {
			ideology = oligarchism
			picture = GFX_LEADER_RSC_YH
			expire = "1965.1.1"
			name = "议会"
		}
		create_country_leader = {
			ideology = falangism
			picture = GFX_LEADER_RSC_YH
			expire = "1965.1.1"
			name = "议会"
		}
		create_country_leader = {
			ideology = socialism
			picture = GFX_LEADER_RSC_YH
			expire = "1965.1.1"
			name = "议会"
		}
		create_country_leader = {
			ideology = leninism
			picture = GFX_LEADER_RSC_YH
			expire = "1965.1.1"
			name = "议会"
		}
		create_country_leader = {
			ideology = nazi
			picture = GFX_LEADER_RSC_YH
			expire = "1965.1.1"
			name = "议会"
		}
	}
	option = {
		name = RYH1.2.b
		ai_chance = {
			base = 25
		}
		set_party_name = {
			ideology = neutrality
			long_name = "议会总部"
			name = "议会总部"
		}
		set_politics = {
			ruling_party = fascism
			elections_allowed = no
		}
		set_popularities = {
			democratic = 6
			communism = 6
			neutrality = 8
			fascism = 80
			extremism = 0
		}
		kill_country_leader = yes
		create_country_leader = {
			ideology = oligarchism
			picture = GFX_LEADER_RSC_YH
			expire = "1965.1.1"
			name = "议会"
		}
		create_country_leader = {
			ideology = falangism
			picture = GFX_LEADER_RSC_YH
			expire = "1965.1.1"
			name = "议会"
		}
		create_country_leader = {
			ideology = socialism
			picture = GFX_LEADER_RSC_YH
			expire = "1965.1.1"
			name = "议会"
		}
		create_country_leader = {
			ideology = leninism
			picture = GFX_LEADER_RSC_YH
			expire = "1965.1.1"
			name = "议会"
		}
		create_country_leader = {
			ideology = nazi
			picture = GFX_LEADER_RSC_YH
			expire = "1965.1.1"
			name = "议会"
		}
	}
	option = {
		name = RYH1.2.c
		ai_chance = {
			base = 25
		}
		set_party_name = {
			ideology = neutrality
			long_name = "议会总部"
			name = "议会总部"
		}
		set_politics = {
			ruling_party = communism
			elections_allowed = no
		}
		set_popularities = {
			democratic = 5
			communism = 80
			neutrality = 5
			fascism = 5
			extremism = 5
		}
		kill_country_leader = yes
		create_country_leader = {
			ideology = oligarchism
			picture = GFX_LEADER_RSC_YH
			expire = "1965.1.1"
			name = "议会"
		}
		create_country_leader = {
			ideology = falangism
			picture = GFX_LEADER_RSC_YH
			expire = "1965.1.1"
			name = "议会"
		}
		create_country_leader = {
			ideology = socialism
			picture = GFX_LEADER_RSC_YH
			expire = "1965.1.1"
			name = "议会"
		}
		create_country_leader = {
			ideology = leninism
			picture = GFX_LEADER_RSC_YH
			expire = "1965.1.1"
			name = "议会"
		}
		create_country_leader = {
			ideology = nazi
			picture = GFX_LEADER_RSC_YH
			expire = "1965.1.1"
			name = "议会"
		}
	}
	option = {
		name = RYH1.2.e
		ai_chance = {
			base = 25
		}
		set_party_name = {
			ideology = neutrality
			long_name = "议会总部"
			name = "议会总部"
		}
		set_politics = {
			ruling_party = fascism
			elections_allowed = no
		}
		set_popularities = {
			democratic = 20
			communism = 20
			neutrality = 20
			fascism = 20
			extremism = 20
		}
		kill_country_leader = yes
		create_country_leader = {
			ideology = oligarchism
			picture = GFX_LEADER_RSC_YH
			expire = "1965.1.1"
			name = "议会"
		}
		create_country_leader = {
			ideology = falangism
			picture = GFX_LEADER_RSC_YH
			expire = "1965.1.1"
			name = "议会"
		}
		create_country_leader = {
			ideology = socialism
			picture = GFX_LEADER_RSC_YH
			expire = "1965.1.1"
			name = "议会"
		}
		create_country_leader = {
			ideology = leninism
			picture = GFX_LEADER_RSC_YH
			expire = "1965.1.1"
			name = "议会"
		}
		create_country_leader = {
			ideology = nazi
			picture = GFX_LEADER_RSC_YH
			expire = "1965.1.1"
			name = "议会"
		}
		custom_effect_tooltip = RYH1.2.e_tooltip
		country_event = {
			id = RYH1.3
			days = 1
		}
		set_party_name = {
			ideology = extremism
			long_name = "俄罗斯新生党"
			name = "俄罗斯新生党"
		}
	}
}

country_event = {
	id = RYH1.3
	title = RYH1.3.t
	desc = RYH1.3.d
	#picture = 
	is_triggered_only = yes
	option = {
		name = RYH1.3.a
		ai_chance = {
			base = 100
		}
		PSK = {
			hidden_effect = {
				add_state_core = 208
				add_state_core = 209
				add_state_core = 210
				add_state_core = 263
				transfer_state = 208
				transfer_state = 209
				transfer_state = 210
				transfer_state = 263
			}
			custom_effect_tooltip = RYH1.3.a_tooltip
		}
		LIK = {
			hidden_effect = {
				add_state_core = 12
				add_state_core = 808
				add_state_core = 809
				add_state_core = 810
				add_state_core = 190
				transfer_state = 12
				transfer_state = 808
				transfer_state = 809
				transfer_state = 810
				transfer_state = 190
			}
			custom_effect_tooltip = RYH1.3.a_tooltip
		}
		SML = {
			hidden_effect = {
				add_state_core = 246
				add_state_core = 242
				add_state_core = 243
				add_state_core = 241
				add_state_core = 224
				transfer_state = 246
				transfer_state = 242
				transfer_state = 243
				transfer_state = 241
				transfer_state = 224
			}
			custom_effect_tooltip = RYH1.3.a_tooltip
		}
		MZZ = {
			hidden_effect = {
				add_state_core = 219
				add_state_core = 205
				add_state_core = 223
				add_state_core = 247
				add_state_core = 254
				add_state_core = 253
				add_state_core = 248
				transfer_state = 219
				transfer_state = 205
				transfer_state = 223
				transfer_state = 247
				transfer_state = 254
				transfer_state = 253
				transfer_state = 248
			}
			custom_effect_tooltip = RYH1.3.a_tooltip
		}
		AMR = {
			hidden_effect = {
				add_state_core = 222
				add_state_core = 193
				add_state_core = 225
				add_state_core = 220
				add_state_core = 240
				add_state_core = 258
				add_state_core = 260
				transfer_state = 222
				transfer_state = 193
				transfer_state = 225
				transfer_state = 220
				transfer_state = 240
				transfer_state = 258
				transfer_state = 260
			}
			custom_effect_tooltip = RYH1.3.a_tooltip
		}
		SGR = {
			hidden_effect = {
				add_state_core = 217
				add_state_core = 245
				add_state_core = 265
				add_state_core = 829
				add_state_core = 239
				add_state_core = 257
				add_state_core = 255
				add_state_core = 252
				transfer_state = 217
				transfer_state = 245
				transfer_state = 265
				transfer_state = 829
				transfer_state = 239
				transfer_state = 257
				transfer_state = 255
				transfer_state = 252
			}
			custom_effect_tooltip = RYH1.3.a_tooltip
		}
		LFZ = {
			hidden_effect = {
				add_state_core = 398
				add_state_core = 573
				add_state_core = 651
				add_state_core = 652
				transfer_state = 398
				transfer_state = 573
				transfer_state = 651
				transfer_state = 652
			}
			custom_effect_tooltip = RYH1.3.a_tooltip_2
		}
		XEL = {
			hidden_effect = {
				add_state_core = 401
				add_state_core = 251
				add_state_core = 250
				add_state_core = 249
				add_state_core = 399
				add_state_core = 256
				add_state_core = 833
				transfer_state = 401
				transfer_state = 251
				transfer_state = 250
				transfer_state = 249
				transfer_state = 399
				transfer_state = 256
				transfer_state = 833
			}
			custom_effect_tooltip = RYH1.3.a_tooltip_2
		}
	}
}
