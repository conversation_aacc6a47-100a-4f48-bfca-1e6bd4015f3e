state = {
	id = 736
	name = "STATE_736" #Name was Istria before the Pola (Istria) split and merge with Fiume
	
	state_category = city

	history = {
		victory_points = {
			6626 3
		}
		owner = WNS
		add_core_of = WNS
		buildings = {
			infrastructure = 1
			dockyard = 1
			arms_factory = 1
			6626 = {
				naval_base = 5
			}

		}

	}

	provinces = {
		599 6626 11595 
	}
	manpower = 1038159 # was 1063159 before Istria was merged with Fiume (~25k pop added to new Istria state (Fiume + Pola)
	buildings_max_level_factor = 1.000

	local_supplies = 0.0 
}
